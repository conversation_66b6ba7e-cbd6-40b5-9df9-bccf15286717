package com.sofa.graphql.service.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sofa.generated.getallstudents.student;

import java.util.List;

public class Data {
    @JsonProperty("student")
    private List<student> student;

    public List<student> getStudent() {
        return student;
    }

    public void setStudent(List<student> student) {
        this.student = student;
    }
}