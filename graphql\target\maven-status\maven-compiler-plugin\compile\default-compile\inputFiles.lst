C:\Users\<USER>\Desktop\chatapp\graphql\graphql\src\main\java\com\sofa\graphql\config\HasuraConfig.java
C:\Users\<USER>\Desktop\chatapp\graphql\graphql\src\main\java\com\sofa\graphql\controller\StudentController.java
C:\Users\<USER>\Desktop\chatapp\graphql\graphql\src\main\java\com\sofa\graphql\DuplicateEmailException.java
C:\Users\<USER>\Desktop\chatapp\graphql\graphql\src\main\java\com\sofa\graphql\GlobalExceptionHandler.java
C:\Users\<USER>\Desktop\chatapp\graphql\graphql\src\main\java\com\sofa\graphql\GraphQlApiApplication.java
C:\Users\<USER>\Desktop\chatapp\graphql\graphql\src\main\java\com\sofa\graphql\GraphQLClientConfig.java
C:\Users\<USER>\Desktop\chatapp\graphql\graphql\src\main\java\com\sofa\graphql\GraphQLRequestException.java
C:\Users\<USER>\Desktop\chatapp\graphql\graphql\src\main\java\com\sofa\graphql\service\HasuraService.java
C:\Users\<USER>\Desktop\chatapp\graphql\graphql\src\main\java\com\sofa\graphql\service\model\Data.java
C:\Users\<USER>\Desktop\chatapp\graphql\graphql\src\main\java\com\sofa\graphql\service\model\GraphQLError.java
C:\Users\<USER>\Desktop\chatapp\graphql\graphql\src\main\java\com\sofa\graphql\service\model\GraphQLResponseWrapper.java
C:\Users\<USER>\Desktop\chatapp\graphql\graphql\src\main\java\com\sofa\graphql\service\model\Student.java
C:\Users\<USER>\Desktop\chatapp\graphql\graphql\src\main\java\com\sofa\graphql\service\StudentMapper.java
C:\Users\<USER>\Desktop\chatapp\graphql\graphql\src\main\java\com\sofa\graphql\StudentErrorResponse.java
C:\Users\<USER>\Desktop\chatapp\graphql\graphql\src\main\java\com\sofa\graphql\StudentRestException.java
