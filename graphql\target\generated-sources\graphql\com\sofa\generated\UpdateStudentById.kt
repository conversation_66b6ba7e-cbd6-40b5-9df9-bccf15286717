package com.sofa.generated

import com.expediagroup.graphql.client.Generated
import com.expediagroup.graphql.client.types.GraphQLClientRequest
import com.fasterxml.jackson.`annotation`.JsonProperty
import com.sofa.generated.updatestudentbyid.student
import kotlin.Int
import kotlin.String
import kotlin.reflect.KClass

public const val UPDATE_STUDENT_BY_ID: String =
    "mutation UpdateStudentById(${'$'}id: bigint!, ${'$'}name: String, ${'$'}email: String, ${'$'}age: Int) {\r\n  update_student_by_pk(pk_columns: {id: ${'$'}id}, _set: {\r\n    name: ${'$'}name,\r\n    email: ${'$'}email,\r\n    age: ${'$'}age\r\n  }) {\r\n    id\r\n    name\r\n    email\r\n    age\r\n  }\r\n}"

@Generated
public class UpdateStudentById(
  override val variables: UpdateStudentById.Variables,
) : GraphQLClientRequest<UpdateStudentById.Result> {
  override val query: String = UPDATE_STUDENT_BY_ID

  override val operationName: String = "UpdateStudentById"

  override fun responseType(): KClass<UpdateStudentById.Result> = UpdateStudentById.Result::class

  @Generated
  public data class Variables(
    @get:JsonProperty(value = "id")
    public val id: bigint,
    @get:JsonProperty(value = "name")
    public val name: String? = null,
    @get:JsonProperty(value = "email")
    public val email: String? = null,
    @get:JsonProperty(value = "age")
    public val age: Int? = null,
  )

  /**
   * mutation root
   */
  @Generated
  public data class Result(
    /**
     * 
     * update single row of the table: "student"
     */
    public val update_student_by_pk: student? = null,
  )
}
