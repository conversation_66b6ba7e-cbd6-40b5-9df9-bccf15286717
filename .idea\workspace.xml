<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="6a791e34-67f9-4494-ad7e-75ab8d2b3a29" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$MAVEN_REPOSITORY$/org/apache/tomcat/embed/tomcat-embed-core/10.1.42/tomcat-embed-core-10.1.42-sources.jar!/org/apache/coyote/AbstractProcessorLight.java" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/graphql/src/main/java/com/sofa/graphql/GraphQlApiApplication.java" root0="SKIP_INSPECTION" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="2zgegiXVGML8AQlRTnWyHdMxBzg" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Application.GraphQlApiApplication.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;dart.analysis.tool.window.visible&quot;: &quot;false&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/chatapp/graphql&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;show.migrate.to.gradle.popup&quot;: &quot;false&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\chatapp\graphql\graphql\src\main\java\com\sofa\graphql" />
    </key>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="6a791e34-67f9-4494-ad7e-75ab8d2b3a29" name="Changes" comment="" />
      <created>1752157256561</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752157256561</updated>
    </task>
    <servers />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/graphql/src/main/java/com/sofa/graphql/service/HasuraService.java</url>
          <line>100</line>
          <properties class="com.sofa.graphql.service.HasuraService" method="deleteStudent">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>