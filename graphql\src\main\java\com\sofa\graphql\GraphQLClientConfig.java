
// import com.expediagroup.graphql.client.GraphQLClient;
// import com.expediagroup.graphql.client.ktor.GraphQLKtorClient;
// import io.ktor.client.HttpClient;
// import io.ktor.client.engine.apache.Apache;
// import org.springframework.context.annotation.Bean;
// import org.springframework.context.annotation.Configuration;
// import java.net.URL;

// @Configuration
// public class GraphQLClientConfig {

//     @Bean
//     public GraphQLClient graphQLClient() {
//         HttpClient httpClient = new HttpClient(Apache.INSTANCE);
//         return new GraphQLKtorClient(
//                 httpClient,
//                 new URL("http://localhost:8080/graphql") // استبدل بعنوان خادم GraphQL الخاص بك
//         );
//     }
// }