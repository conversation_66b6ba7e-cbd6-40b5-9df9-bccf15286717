spring.application.name=GraphQlAPI

spring.datasource.url=****************************************
spring.datasource.username=zayed
spring.datasource.password=zayed
spring.datasource.driver-class-name=org.postgresql.Driver
server.port=8085
# JPA Configuration
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=true

# Liquibase Configuration
spring.liquibase.change-log=classpath:db/changelog/db.changelog-master.yaml
spring.liquibase.enabled=true
spring.jpa.open-in-view=false

# GraphQL Client Configuration
hasura.graphql.endpoint=http://localhost:8081/v1/graphql
# Swagger Configuration (????)
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.api-docs.path=/v3/api-docs
springdoc.swagger-ui.enabled=true
springdoc.cache.disabled=true
springdoc.show-actuator=true

# ??????? ??????? ??????? (???)
spring.mvc.static-path-pattern=/**
spring.web.resources.static-locations=classpath:/META-INF/resources/