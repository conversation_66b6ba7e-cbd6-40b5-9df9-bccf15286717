package com.sofa.generated

import com.expediagroup.graphql.client.Generated
import com.expediagroup.graphql.client.types.GraphQLClientRequest
import com.fasterxml.jackson.`annotation`.JsonProperty
import com.sofa.generated.deletestudentbyid.student
import kotlin.String
import kotlin.reflect.KClass

public const val DELETE_STUDENT_BY_ID: String =
    "mutation DeleteStudentById(${'$'}id: bigint!) {\r\n  delete_student_by_pk(id: ${'$'}id) {\r\n    id\r\n    name\r\n    email\r\n    age\r\n  }\r\n}"

@Generated
public class DeleteStudentById(
  override val variables: DeleteStudentById.Variables,
) : GraphQLClientRequest<DeleteStudentById.Result> {
  override val query: String = DELETE_STUDENT_BY_ID

  override val operationName: String = "DeleteStudentById"

  override fun responseType(): KClass<DeleteStudentById.Result> = DeleteStudentById.Result::class

  @Generated
  public data class Variables(
      @get:JsonProperty(value = "id")
    public val id: Long,
  )

  /**
   * mutation root
   */
  @Generated
  public data class Result(
    /**
     * 
     * delete single row from the table: "student"
     */
    public val delete_student_by_pk: student? = null,
  )
}
