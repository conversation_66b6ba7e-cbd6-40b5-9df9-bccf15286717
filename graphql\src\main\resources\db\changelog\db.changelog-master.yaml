databaseChangeLog:
  - changeSet:
      id: 001-create-student-table
      author: zayed
      changes:
        - createTable:
            tableName: student
            columns:
              - column:
                  name: id
                  type: BIGINT
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: VARCHAR(100)
                  constraints:
                    nullable: false
              - column:
                  name: email
                  type: VARCHAR(50)
                  constraints:
                    nullable: false
                    unique: true
              - column:
                  name: age
                  type: INT
                  constraints:
                    nullable: false
      rollback:
        - dropTable:
            tableName: student
