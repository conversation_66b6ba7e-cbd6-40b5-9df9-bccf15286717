package com.sofa.generated

import com.expediagroup.graphql.client.Generated
import com.expediagroup.graphql.client.types.GraphQLClientRequest
import com.fasterxml.jackson.`annotation`.JsonProperty
import com.sofa.generated.insertnewstudent.student
import kotlin.Int
import kotlin.String
import kotlin.reflect.KClass

public const val INSERT_NEW_STUDENT: String =
    "mutation InsertNewStudent(${'$'}name: String!, ${'$'}email: String!, ${'$'}age: Int!) {\r\n  insert_student_one(object: {\r\n    name: ${'$'}name,\r\n    email: ${'$'}email,\r\n    age: ${'$'}age\r\n  }) {\r\n    id\r\n    name\r\n    email\r\n    age\r\n  }\r\n}"

@Generated
public class InsertNewStudent(
  override val variables: InsertNewStudent.Variables,
) : GraphQLClientRequest<InsertNewStudent.Result> {
  override val query: String = INSERT_NEW_STUDENT

  override val operationName: String = "InsertNewStudent"

  override fun responseType(): KClass<InsertNewStudent.Result> = InsertNewStudent.Result::class

  @Generated
  public data class Variables(
    @get:JsonProperty(value = "name")
    public val name: String,
    @get:JsonProperty(value = "email")
    public val email: String,
    @get:JsonProperty(value = "age")
    public val age: Int,
  )

  /**
   * mutation root
   */
  @Generated
  public data class Result(
    /**
     * 
     * insert a single row into the table: "student"
     */
    public val insert_student_one: student? = null,
  )
}
