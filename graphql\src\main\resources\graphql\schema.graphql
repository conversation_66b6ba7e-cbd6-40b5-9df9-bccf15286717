schema {
  query: query_root
  mutation: mutation_root
  subscription: subscription_root
}

"""whether this query should be cached (Hasura Cloud only)"""
directive @cached(
  """measured in seconds"""
  ttl: Int! = 60

  """refresh the cache entry"""
  refresh: Boolean! = false
) on QUERY

"""
Boolean expression to compare columns of type "Int". All fields are combined with logical 'AND'.
"""
input Int_comparison_exp {
  _eq: Int
  _gt: Int
  _gte: Int
  _in: [Int!]
  _is_null: Boolean
  _lt: Int
  _lte: Int
  _neq: Int
  _nin: [Int!]
}

"""
Boolean expression to compare columns of type "String". All fields are combined with logical 'AND'.
"""
input String_comparison_exp {
  _eq: String
  _gt: String
  _gte: String

  """does the column match the given case-insensitive pattern"""
  _ilike: String
  _in: [String!]

  """
  does the column match the given POSIX regular expression, case insensitive
  """
  _iregex: String
  _is_null: Boolean

  """does the column match the given pattern"""
  _like: String
  _lt: String
  _lte: String
  _neq: String

  """does the column NOT match the given case-insensitive pattern"""
  _nilike: String
  _nin: [String!]

  """
  does the column NOT match the given POSIX regular expression, case insensitive
  """
  _niregex: String

  """does the column NOT match the given pattern"""
  _nlike: String

  """
  does the column NOT match the given POSIX regular expression, case sensitive
  """
  _nregex: String

  """does the column NOT match the given SQL regular expression"""
  _nsimilar: String

  """
  does the column match the given POSIX regular expression, case sensitive
  """
  _regex: String

  """does the column match the given SQL regular expression"""
  _similar: String
}

scalar bigint

"""
Boolean expression to compare columns of type "bigint". All fields are combined with logical 'AND'.
"""
input bigint_comparison_exp {
  _eq: bigint
  _gt: bigint
  _gte: bigint
  _in: [bigint!]
  _is_null: Boolean
  _lt: bigint
  _lte: bigint
  _neq: bigint
  _nin: [bigint!]
}

"""ordering argument of a cursor"""
enum cursor_ordering {
  """ascending ordering of the cursor"""
  ASC

  """descending ordering of the cursor"""
  DESC
}

"""mutation root"""
type mutation_root {
  """
  delete data from the table: "student"
  """
  delete_student(
    """filter the rows which have to be deleted"""
    where: student_bool_exp!
  ): student_mutation_response

  """
  delete single row from the table: "student"
  """
  delete_student_by_pk(id: bigint!): student

  """
  insert data into the table: "student"
  """
  insert_student(
    """the rows to be inserted"""
    objects: [student_insert_input!]!

    """upsert condition"""
    on_conflict: student_on_conflict
  ): student_mutation_response

  """
  insert a single row into the table: "student"
  """
  insert_student_one(
    """the row to be inserted"""
    object: student_insert_input!

    """upsert condition"""
    on_conflict: student_on_conflict
  ): student

  """
  update data of the table: "student"
  """
  update_student(
    """increments the numeric columns with given value of the filtered values"""
    _inc: student_inc_input

    """sets the columns of the filtered rows to the given values"""
    _set: student_set_input

    """filter the rows which have to be updated"""
    where: student_bool_exp!
  ): student_mutation_response

  """
  update single row of the table: "student"
  """
  update_student_by_pk(
    """increments the numeric columns with given value of the filtered values"""
    _inc: student_inc_input

    """sets the columns of the filtered rows to the given values"""
    _set: student_set_input
    pk_columns: student_pk_columns_input!
  ): student

  """
  update multiples rows of table: "student"
  """
  update_student_many(
    """updates to execute, in order"""
    updates: [student_updates!]!
  ): [student_mutation_response]
}

"""column ordering options"""
enum order_by {
  """in ascending order, nulls last"""
  asc

  """in ascending order, nulls first"""
  asc_nulls_first

  """in ascending order, nulls last"""
  asc_nulls_last

  """in descending order, nulls first"""
  desc

  """in descending order, nulls first"""
  desc_nulls_first

  """in descending order, nulls last"""
  desc_nulls_last
}

type query_root {
  """
  fetch data from the table: "student"
  """
  student(
    """distinct select on columns"""
    distinct_on: [student_select_column!]

    """limit the number of rows returned"""
    limit: Int

    """skip the first n rows. Use only with order_by"""
    offset: Int

    """sort the rows by one or more columns"""
    order_by: [student_order_by!]

    """filter the rows returned"""
    where: student_bool_exp
  ): [student!]!

  """
  fetch aggregated fields from the table: "student"
  """
  student_aggregate(
    """distinct select on columns"""
    distinct_on: [student_select_column!]

    """limit the number of rows returned"""
    limit: Int

    """skip the first n rows. Use only with order_by"""
    offset: Int

    """sort the rows by one or more columns"""
    order_by: [student_order_by!]

    """filter the rows returned"""
    where: student_bool_exp
  ): student_aggregate!

  """fetch data from the table: "student" using primary key columns"""
  student_by_pk(id: bigint!): student
}

"""
columns and relationships of "student"
"""
type student {
  age: Int!
  email: String!
  id: bigint!
  name: String!
}

"""
aggregated selection of "student"
"""
type student_aggregate {
  aggregate: student_aggregate_fields
  nodes: [student!]!
}

"""
aggregate fields of "student"
"""
type student_aggregate_fields {
  avg: student_avg_fields
  count(columns: [student_select_column!], distinct: Boolean): Int!
  max: student_max_fields
  min: student_min_fields
  stddev: student_stddev_fields
  stddev_pop: student_stddev_pop_fields
  stddev_samp: student_stddev_samp_fields
  sum: student_sum_fields
  var_pop: student_var_pop_fields
  var_samp: student_var_samp_fields
  variance: student_variance_fields
}

"""aggregate avg on columns"""
type student_avg_fields {
  age: Float
  id: Float
}

"""
Boolean expression to filter rows from the table "student". All fields are combined with a logical 'AND'.
"""
input student_bool_exp {
  _and: [student_bool_exp!]
  _not: student_bool_exp
  _or: [student_bool_exp!]
  age: Int_comparison_exp
  email: String_comparison_exp
  id: bigint_comparison_exp
  name: String_comparison_exp
}

"""
unique or primary key constraints on table "student"
"""
enum student_constraint {
  """
  unique or primary key constraint on columns "email"
  """
  student_email_key

  """
  unique or primary key constraint on columns "id"
  """
  student_pkey
}

"""
input type for incrementing numeric columns in table "student"
"""
input student_inc_input {
  age: Int
  id: bigint
}

"""
input type for inserting data into table "student"
"""
input student_insert_input {
  age: Int
  email: String
  id: bigint
  name: String
}

"""aggregate max on columns"""
type student_max_fields {
  age: Int
  email: String
  id: bigint
  name: String
}

"""aggregate min on columns"""
type student_min_fields {
  age: Int
  email: String
  id: bigint
  name: String
}

"""
response of any mutation on the table "student"
"""
type student_mutation_response {
  """number of rows affected by the mutation"""
  affected_rows: Int!

  """data from the rows affected by the mutation"""
  returning: [student!]!
}

"""
on_conflict condition type for table "student"
"""
input student_on_conflict {
  constraint: student_constraint!
  update_columns: [student_update_column!]! = []
  where: student_bool_exp
}

"""Ordering options when selecting data from "student"."""
input student_order_by {
  age: order_by
  email: order_by
  id: order_by
  name: order_by
}

"""primary key columns input for table: student"""
input student_pk_columns_input {
  id: bigint!
}

"""
select columns of table "student"
"""
enum student_select_column {
  """column name"""
  age

  """column name"""
  email

  """column name"""
  id

  """column name"""
  name
}

"""
input type for updating data in table "student"
"""
input student_set_input {
  age: Int
  email: String
  id: bigint
  name: String
}

"""aggregate stddev on columns"""
type student_stddev_fields {
  age: Float
  id: Float
}

"""aggregate stddev_pop on columns"""
type student_stddev_pop_fields {
  age: Float
  id: Float
}

"""aggregate stddev_samp on columns"""
type student_stddev_samp_fields {
  age: Float
  id: Float
}

"""
Streaming cursor of the table "student"
"""
input student_stream_cursor_input {
  """Stream column input with initial value"""
  initial_value: student_stream_cursor_value_input!

  """cursor ordering"""
  ordering: cursor_ordering
}

"""Initial value of the column from where the streaming should start"""
input student_stream_cursor_value_input {
  age: Int
  email: String
  id: bigint
  name: String
}

"""aggregate sum on columns"""
type student_sum_fields {
  age: Int
  id: bigint
}

"""
update columns of table "student"
"""
enum student_update_column {
  """column name"""
  age

  """column name"""
  email

  """column name"""
  id

  """column name"""
  name
}

input student_updates {
  """increments the numeric columns with given value of the filtered values"""
  _inc: student_inc_input

  """sets the columns of the filtered rows to the given values"""
  _set: student_set_input

  """filter the rows which have to be updated"""
  where: student_bool_exp!
}

"""aggregate var_pop on columns"""
type student_var_pop_fields {
  age: Float
  id: Float
}

"""aggregate var_samp on columns"""
type student_var_samp_fields {
  age: Float
  id: Float
}

"""aggregate variance on columns"""
type student_variance_fields {
  age: Float
  id: Float
}

type subscription_root {
  """
  fetch data from the table: "student"
  """
  student(
    """distinct select on columns"""
    distinct_on: [student_select_column!]

    """limit the number of rows returned"""
    limit: Int

    """skip the first n rows. Use only with order_by"""
    offset: Int

    """sort the rows by one or more columns"""
    order_by: [student_order_by!]

    """filter the rows returned"""
    where: student_bool_exp
  ): [student!]!

  """
  fetch aggregated fields from the table: "student"
  """
  student_aggregate(
    """distinct select on columns"""
    distinct_on: [student_select_column!]

    """limit the number of rows returned"""
    limit: Int

    """skip the first n rows. Use only with order_by"""
    offset: Int

    """sort the rows by one or more columns"""
    order_by: [student_order_by!]

    """filter the rows returned"""
    where: student_bool_exp
  ): student_aggregate!

  """fetch data from the table: "student" using primary key columns"""
  student_by_pk(id: bigint!): student

  """
  fetch data from the table in a streaming manner: "student"
  """
  student_stream(
    """maximum number of rows returned in a single batch"""
    batch_size: Int!

    """cursor to stream the results returned by the query"""
    cursor: [student_stream_cursor_input]!

    """filter the rows returned"""
    where: student_bool_exp
  ): [student!]!
}

