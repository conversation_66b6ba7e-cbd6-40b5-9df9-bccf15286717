spring.application.name=GraphQlAPI

spring.datasource.url=****************************************
spring.datasource.username=zayed
spring.datasource.password=zayed
spring.datasource.driver-class-name=org.postgresql.Driver
server.port=8085
# JPA Configuration
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=true

# Liquibase Configuration
spring.liquibase.change-log=classpath:db/changelog/db.changelog-master.yaml
spring.liquibase.enabled=true

# GraphQL Client Configuration
hasura.graphql.endpoint=http://localhost:8081/v1/graphql
graphql.client.headers.x-hasura-admin-secret=myadminsecretkey