

services:
  postgres:
    image: postgres:16
    restart: always
    volumes:
      - db_data:/var/lib/postgresql/data
    env_file:
      - .env
    ports:
      - "5434:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $$POSTGRES_USER -d $$POSTGRES_DB"]
      interval: 5s
      timeout: 5s
      retries: 5

  hasura:
    image: hasura/graphql-engine:latest
    ports:
      - "8081:8080"
    restart: always
    env_file:
      - .env
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      HASURA_GRAPHQL_DATABASE_URL: ************************************/graphql
      HASURA_GRAPHQL_ENABLE_CONSOLE: "true"
      # HASURA_GRAPHQL_ADMIN_SECRET: myadminsecretkey
      HASURA_GRAPHQL_ENABLED_APIS: "graphql,metadata"
      # HASURA_GRAPHQL_UNAUTHORIZED_ROLE: "public"
      HASURA_GRAPHQL_ENABLE_CORS: "true"
      HASURA_GRAPHQL_CORS_DOMAIN: "*"

volumes:
  db_data:
