package com.sofa.generated

import com.expediagroup.graphql.client.Generated
import com.expediagroup.graphql.client.types.GraphQLClientRequest
import com.sofa.generated.getallstudents.student
import kotlin.String
import kotlin.collections.List
import kotlin.reflect.KClass

public const val GET_ALL_STUDENTS: String =
    "query GetAllStudents {\n  student {\n    id\n    name\n    email\n    age\n  }\n}"

@Generated
public class GetAllStudents : GraphQLClientRequest<GetAllStudents.Result> {
  override val query: String = GET_ALL_STUDENTS

  override val operationName: String = "GetAllStudents"

  override fun responseType(): KClass<GetAllStudents.Result> = GetAllStudents.Result::class

  @Generated
  public data class Result(
    /**
     * 
     * fetch data from the table: "student"
     */
    public val student: List<student>,
  )
}
