package com.sofa.generated

import com.expediagroup.graphql.client.Generated
import com.expediagroup.graphql.client.types.GraphQLClientRequest
import com.fasterxml.jackson.`annotation`.JsonProperty
import com.sofa.generated.getstudentbyid.student
import kotlin.String
import kotlin.reflect.KClass

public const val GET_STUDENT_BY_ID: String =
    "query GetStudentById(${'$'}id: bigint!) {\r\n  student_by_pk(id: ${'$'}id) {\r\n    id\r\n    name\r\n    email\r\n    age\r\n  }\r\n}"

@Generated
public class GetStudentById(
  override val variables: GetStudentById.Variables,
) : GraphQLClientRequest<GetStudentById.Result> {
  override val query: String = GET_STUDENT_BY_ID

  override val operationName: String = "GetStudentById"

  override fun responseType(): KClass<GetStudentById.Result> = GetStudentById.Result::class

  @Generated
  public data class Variables(
      @get:JsonProperty(value = "id")
    public val id: Long,
  )

  @Generated
  public data class Result(
    /**
     * fetch data from the table: "student" using primary key columns
     */
    public val student_by_pk: student? = null,
  )
}
